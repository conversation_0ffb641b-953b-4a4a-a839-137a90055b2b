// Copyright (c) VisionMaster Team. All Rights Reserved.
// Licensed under the MIT License (the "License")

using H.VisionMaster.PluginInterface.Base;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace H.VisionMaster.PluginInterface.Services
{
    /// <summary>
    /// 插件加载器服务实现
    /// </summary>
    public class PluginLoaderService : IPluginLoaderService
    {
        private readonly ConcurrentDictionary<string, Assembly> _loadedAssemblies = new();
        private readonly ConcurrentDictionary<string, PluginInfo> _pluginInfos = new();
        private readonly ConcurrentDictionary<string, DateTime> _pluginTimestamps = new();
        private readonly List<IOpenCVNodeData> _loadedPlugins = new();
        private readonly object _lockObject = new object();

        public IReadOnlyList<IOpenCVNodeData> LoadedPlugins
        {
            get
            {
                lock (_lockObject)
                {
                    return _loadedPlugins.ToList().AsReadOnly();
                }
            }
        }

        public event EventHandler<PluginLoadedEventArgs> PluginLoaded;
        public event EventHandler<PluginUnloadedEventArgs> PluginUnloaded;

        public async Task<PluginLoadResult> LoadPluginsFromDirectoryAsync(string pluginDirectory)
        {
            var result = new PluginLoadResult { IsSuccess = true };

            if (!Directory.Exists(pluginDirectory))
            {
                result.IsSuccess = false;
                result.ErrorMessages.Add($"插件目录不存在: {pluginDirectory}");
                return result;
            }

            var dllFiles = Directory.GetFiles(pluginDirectory, "*.dll", SearchOption.AllDirectories)
                .Where(f => Path.GetFileName(f).StartsWith("Plugin."))
                .ToArray();

            foreach (var dllFile in dllFiles)
            {
                try
                {
                    var loadResult = await LoadPluginFromAssemblyAsync(dllFile);
                    result.LoadedCount += loadResult.LoadedCount;
                    result.FailedCount += loadResult.FailedCount;
                    result.ErrorMessages.AddRange(loadResult.ErrorMessages);
                    result.LoadedPlugins.AddRange(loadResult.LoadedPlugins);
                }
                catch (Exception ex)
                {
                    result.FailedCount++;
                    result.ErrorMessages.Add($"加载插件失败 {dllFile}: {ex.Message}");
                }
            }

            result.IsSuccess = result.FailedCount == 0;
            return result;
        }

        public async Task<PluginLoadResult> LoadPluginFromAssemblyAsync(string assemblyPath)
        {
            var result = new PluginLoadResult { IsSuccess = true };

            try
            {
                await Task.Run(() =>
                {
                    // 检查文件时间戳，避免重复加载
                    var fileInfo = new FileInfo(assemblyPath);
                    var fileName = Path.GetFileName(assemblyPath);

                    if (_pluginTimestamps.ContainsKey(fileName) &&
                        _pluginTimestamps[fileName] >= fileInfo.LastWriteTime)
                    {
                        return; // 跳过未修改的插件
                    }

                    var assembly = Assembly.LoadFrom(assemblyPath);
                    _loadedAssemblies[assemblyPath] = assembly;
                    _pluginTimestamps[fileName] = fileInfo.LastWriteTime;

                    var pluginTypes = assembly.GetTypes()
                        .Where(t => t.IsClass && !t.IsAbstract)
                        .Where(t => typeof(IOpenCVNodeData).IsAssignableFrom(t))
                        .ToArray();

                    foreach (var pluginType in pluginTypes)
                    {
                        try
                        {
                            var instance = Activator.CreateInstance(pluginType) as IOpenCVNodeData;
                            if (instance != null)
                            {
                                var metadata = instance.GetMetadata();
                                var pluginInfo = new PluginInfo
                                {
                                    Name = metadata.Name,
                                    Version = metadata.Version,
                                    Description = metadata.Description,
                                    Author = metadata.Author,
                                    AssemblyPath = assemblyPath,
                                    PluginType = pluginType
                                };

                                _pluginInfos[metadata.Name] = pluginInfo;
                                
                                lock (_lockObject)
                                {
                                    _loadedPlugins.Add(instance);
                                }

                                result.LoadedCount++;
                                result.LoadedPlugins.Add(pluginInfo);

                                // 触发插件加载事件
                                PluginLoaded?.Invoke(this, new PluginLoadedEventArgs
                                {
                                    PluginInfo = pluginInfo,
                                    PluginInstance = instance
                                });
                            }
                        }
                        catch (Exception ex)
                        {
                            result.FailedCount++;
                            result.ErrorMessages.Add($"创建插件实例失败 {pluginType.Name}: {ex.Message}");
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.FailedCount++;
                result.ErrorMessages.Add($"加载程序集失败 {assemblyPath}: {ex.Message}");
            }

            return result;
        }

        public bool UnloadPlugin(string pluginName)
        {
            if (_pluginInfos.TryRemove(pluginName, out var pluginInfo))
            {
                lock (_lockObject)
                {
                    var plugin = _loadedPlugins.FirstOrDefault(p => p.PluginName == pluginName);
                    if (plugin != null)
                    {
                        _loadedPlugins.Remove(plugin);
                        plugin.Dispose();
                    }
                }

                PluginUnloaded?.Invoke(this, new PluginUnloadedEventArgs { PluginName = pluginName });
                return true;
            }

            return false;
        }

        public IEnumerable<T> GetPlugins<T>() where T : class
        {
            lock (_lockObject)
            {
                return _loadedPlugins.OfType<T>().ToList();
            }
        }

        public IEnumerable<T> GetLoadedPlugins<T>() where T : class
        {
            lock (_lockObject)
            {
                return _loadedPlugins.OfType<T>().ToList();
            }
        }

        public IOpenCVNodeData CreatePluginInstance(string pluginName)
        {
            if (_pluginInfos.TryGetValue(pluginName, out var pluginInfo))
            {
                try
                {
                    return Activator.CreateInstance(pluginInfo.PluginType) as IOpenCVNodeData;
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"创建插件实例失败: {pluginName}", ex);
                }
            }

            throw new ArgumentException($"未找到插件: {pluginName}");
        }

        public async Task<PluginLoadResult> ReloadPluginsAsync(string pluginDirectory)
        {
            // 清理现有插件
            lock (_lockObject)
            {
                foreach (var plugin in _loadedPlugins)
                {
                    plugin.Dispose();
                }
                _loadedPlugins.Clear();
            }

            _pluginInfos.Clear();
            _loadedAssemblies.Clear();
            _pluginTimestamps.Clear();

            // 重新加载
            return await LoadPluginsFromDirectoryAsync(pluginDirectory);
        }
    }
}
