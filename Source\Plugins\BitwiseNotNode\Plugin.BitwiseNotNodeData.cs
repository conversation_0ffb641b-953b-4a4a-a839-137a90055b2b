﻿// Copyright (c) VisionMaster Team. All Rights Reserved.
// Licensed under the MIT License (the "License")

global using H.Common.Attributes;
global using H.Extensions.FontIcon;
global using H.VisionMaster.OpenCV.Base;
global using H.VisionMaster.NodeGroup.Groups.Preprocessings;
global using H.VisionMaster.NodeData.Base;
global using H.Controls.Diagram.Presenter.DiagramDatas.Base;
global using H.Controls.Diagram.Presenter.Flowables;
global using H.VisionMaster.PluginInterface.Base;
global using OpenCvSharp;
global using System.ComponentModel.DataAnnotations;

namespace Plugin.BitwiseNotNode
{
    /// <summary>
    /// 反转黑白插件 - 二值图片的效果反转
    /// </summary>
    [Icon(FontIcons.Color)]
    [Display(Name = "反转黑白2", GroupName = "基础函数", Description = "二值图片的效果反转既黑色变白色，白色变黑色", Order = 21)]
    public class BitwiseNotNodeData : OpenCVNodeDataBase, IPreprocessingGroupableNodeData, H.VisionMaster.PluginInterface.Base.IOpenCVNodeData
    {
        // 实现插件接口属性
        public string PluginName => "反转黑白2";
        public string PluginVersion => "1.0.0";
        public string PluginDescription => "二值图片的效果反转既黑色变白色，白色变黑色";
        public string PluginAuthor => "VisionMaster Team";

        protected override H.Controls.Diagram.Presenter.Flowables.FlowableResult<Mat> Invoke(ISrcVisionNodeData<Mat> srcImageNodeData, IVisionNodeData<Mat> from, IFlowableDiagramData diagram)
        {
            try
            {
                if (from?.Mat == null || from.Mat.Empty())
                    return this.Error(null, "输入图像无效");

                Mat result = new Mat();
                Cv2.BitwiseNot(from.Mat, result);

                return this.OK(result, "反转处理完成");
            }
            catch (System.Exception ex)
            {
                return this.Error(from?.Mat, $"反转处理失败: {ex.Message}");
            }
        }

        // 实现插件接口方法
        public H.VisionMaster.PluginInterface.Base.FlowableResult<Mat> Execute(Mat inputMat, object parameters = null)
        {
            try
            {
                if (inputMat == null || inputMat.Empty())
                    return new H.VisionMaster.PluginInterface.Base.FlowableResult<Mat> { IsSuccess = false, ErrorMessage = "输入图像无效" };

                Mat result = new Mat();
                Cv2.BitwiseNot(inputMat, result);

                return new H.VisionMaster.PluginInterface.Base.FlowableResult<Mat> { IsSuccess = true, Data = result, Message = "反转处理完成" };
            }
            catch (System.Exception ex)
            {
                return new H.VisionMaster.PluginInterface.Base.FlowableResult<Mat> { IsSuccess = false, ErrorMessage = $"反转处理失败: {ex.Message}", Exception = ex };
            }
        }

        // 实现插件元数据方法
        public PluginMetadata GetMetadata()
        {
            return new PluginMetadata
            {
                Name = PluginName,
                Version = PluginVersion,
                Description = PluginDescription,
                Author = PluginAuthor,
                Category = "图像预处理",
                Order = 21,
                CreateDate = new System.DateTime(2024, 1, 1)
            };
        }

        // 实现插件接口的验证方法
        public bool IsValidInput(Mat input)
        {
            return input != null && !input.Empty();
        }
    }
}
